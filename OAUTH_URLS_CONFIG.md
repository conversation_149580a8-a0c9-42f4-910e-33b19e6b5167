# 🔧 OAuth URLs Configuration Guide

## 🎯 Google Cloud Console Configuration

### 📍 **Authorized JavaScript Origins:**
```
http://localhost:3001
http://localhost:8001
https://fgpyqyiazgouorgpkavr.supabase.co
```

### 📍 **Authorized Redirect URIs:**
```
http://localhost:8001/api/v1/auth/callback
http://localhost:3001/auth/callback
https://fgpyqyiazgouorgpkavr.supabase.co/auth/v1/callback
```

## 🎯 Supabase Auth Configuration

### 📍 **Site URL:**
```
http://localhost:3001
```

### 📍 **Redirect URLs:**
```
http://localhost:3001/auth/callback
http://localhost:3001/**
```

### 📍 **Google OAuth Provider:**
- **Client ID:** `************-0fcheup5gm7naos0cubblmueftb6viqp.apps.googleusercontent.com`
- **Client Secret:** `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`

## 🔄 OAuth Flow Options

### **Option 1: Direct Backend OAuth**
1. Frontend → `http://localhost:8001/api/v1/auth/oauth/google`
2. Backend → Redirect to Google OAuth
3. Google → Callback to `http://localhost:8001/api/v1/auth/callback`
4. Backend → Process & redirect to `http://localhost:3001/auth/callback?access_token=xxx`

### **Option 2: Supabase Auth (Recommended)**
1. Frontend → Use Supabase Auth SDK
2. Supabase → Handle Google OAuth flow
3. Frontend → Get access_token from Supabase
4. Frontend → Send access_token to Backend for verification

## 📝 Backend Environment Variables

```env
# OAuth Configuration
GOOGLE_CLIENT_ID=************-0fcheup5gm7naos0cubblmueftb6viqp.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# OAuth Redirect URIs
GOOGLE_REDIRECT_URI=http://localhost:8001/api/v1/auth/callback

# Frontend URL
FRONTEND_URL=http://localhost:3001

# Supabase Configuration
SUPABASE_URL=https://fgpyqyiazgouorgpkavr.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.mi6eHu3jJ9K2RXBz71IKCDNBGs9bnDPBf2a8-IcuvYI
```

## 🧪 Testing URLs

- **Backend OAuth:** `http://localhost:8001/api/v1/auth/oauth/google`
- **Backend Health:** `http://localhost:8001/api/v1/test/health`
- **API Docs:** `http://localhost:8001/docs`
- **Frontend:** `http://localhost:3001`

## ⚠️ Important Notes

1. **Google Console:** Pastikan semua redirect URIs sudah ditambahkan
2. **Supabase:** Enable Google provider dengan credentials yang benar
3. **CORS:** Backend sudah dikonfigurasi untuk allow localhost:3001
4. **HTTPS:** Untuk production, ganti semua localhost dengan domain HTTPS
