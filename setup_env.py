#!/usr/bin/env python3
"""
Setup environment variables for ScapeGIS Backend
"""
import os
import getpass

def setup_environment():
    """Setup environment variables for the application"""
    
    print("🔧 ScapeGIS Backend Environment Setup")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = ".env"
    env_vars = {}
    
    if os.path.exists(env_file):
        print(f"📁 Found existing {env_file} file")
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    
    print("\n📋 Current environment variables:")
    for key, value in env_vars.items():
        if 'PASSWORD' in key or 'KEY' in key:
            print(f"  {key}: {'*' * len(value)}")
        else:
            print(f"  {key}: {value}")
    
    print("\n🔐 Setup Database Configuration:")
    print("   Get your database password from Supabase Dashboard:")
    print("   1. Go to https://supabase.com/dashboard")
    print("   2. Select your project: fgpyqyiazgouorgpkavr")
    print("   3. Go to Settings → Database")
    print("   4. Copy the password from Connection string")
    
    # Get database password
    db_password = getpass.getpass("   Enter your Supabase database password: ")
    if db_password:
        env_vars['DATABASE_PASSWORD'] = db_password
    
    # Get service role key
    print("\n🔑 Setup Service Role Key:")
    print("   Get your service role key from Supabase Dashboard:")
    print("   1. Go to Settings → API")
    print("   2. Copy the 'service_role' key (not anon key)")
    
    service_role_key = getpass.getpass("   Enter your Supabase service role key: ")
    if service_role_key:
        env_vars['SUPABASE_SERVICE_ROLE_KEY'] = service_role_key
    
    # Write to .env file
    print(f"\n💾 Writing to {env_file} file...")
    with open(env_file, 'w') as f:
        f.write("# ScapeGIS Backend Environment Variables\n")
        f.write("# Generated by setup_env.py\n\n")
        
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")
    
    print(f"✅ Environment variables saved to {env_file}")
    print("\n🚀 Next steps:")
    print("   1. Restart your server: python start_server.py")
    print("   2. Test the connection: curl http://localhost:8001/api/v1/projects/recent")
    
    return True

if __name__ == "__main__":
    setup_environment() 