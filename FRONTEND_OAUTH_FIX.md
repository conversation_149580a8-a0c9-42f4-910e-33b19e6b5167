# 🔧 Frontend OAuth Fix - React Implementation

## 🎯 Masalah yang Teridentifikasi

Berdasarkan URL yang Anda berikan:
```
http://localhost:3001/auth/error?error=no_code#access_token=eyJhbGciOiJIUzI1NiIs...
```

**Masalah:** Frontend tidak menangani `access_token` yang ada di URL fragment (`#access_token=...`)

## ✅ Solusi Frontend React

### 1. **Komponen OAuth Callback Handler**

```jsx
// src/components/auth/OAuthCallback.jsx
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '../lib/supabase';

const OAuthCallback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = async () => {
    try {
      // Get access_token from URL fragment
      const hashParams = new URLSearchParams(location.hash.substring(1));
      const accessToken = hashParams.get('access_token');
      const refreshToken = hashParams.get('refresh_token');
      
      if (accessToken) {
        console.log('✅ Access token found:', accessToken);
        
        // Send access_token to backend for verification and user creation
        const response = await fetch('http://localhost:8001/api/v1/auth/supabase-callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            access_token: accessToken,
            refresh_token: refreshToken
          })
        });

        if (response.ok) {
          const data = await response.json();
          
          // Store tokens in localStorage
          localStorage.setItem('access_token', data.access_token);
          localStorage.setItem('user', JSON.stringify(data.user));
          
          console.log('✅ OAuth success:', data);
          
          // Redirect to dashboard
          navigate('/dashboard', { replace: true });
        } else {
          const errorData = await response.json();
          throw new Error(errorData.detail || 'Authentication failed');
        }
      } else {
        // Check URL query params for error
        const urlParams = new URLSearchParams(location.search);
        const errorParam = urlParams.get('error');
        
        if (errorParam) {
          throw new Error(`OAuth error: ${errorParam}`);
        } else {
          throw new Error('No access token found in callback');
        }
      }
    } catch (err) {
      console.error('❌ OAuth callback error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Processing authentication...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">❌ Authentication Error</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => navigate('/login')}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  return null;
};

export default OAuthCallback;
```

### 2. **Login Component dengan Supabase Auth**

```jsx
// src/components/auth/Login.jsx
import React, { useState } from 'react';
import { supabase } from '../lib/supabase';

const Login = () => {
  const [loading, setLoading] = useState(false);

  const handleGoogleLogin = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'http://localhost:3001/auth/callback'
        }
      });

      if (error) {
        console.error('❌ Google login error:', error);
        alert('Login failed: ' + error.message);
      }
    } catch (err) {
      console.error('❌ Login error:', err);
      alert('Login failed: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to ScapeGIS
          </h2>
        </div>
        <div>
          <button
            onClick={handleGoogleLogin}
            disabled={loading}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {loading ? (
              <span className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Signing in...
              </span>
            ) : (
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
              </span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
```

### 3. **Supabase Configuration**

```javascript
// src/lib/supabase.js
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgpyqyiazgouorgpkavr.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZncHlxeWlhemdvdW9yZ3BrYXZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2OTY4NDgsImV4cCI6MjA2NjI3Mjg0OH0.mi6eHu3jJ9K2RXBz71IKCDNBGs9bnDPBf2a8-IcuvYI'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### 4. **Router Setup**

```jsx
// src/App.jsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Login from './components/auth/Login';
import OAuthCallback from './components/auth/OAuthCallback';
import Dashboard from './components/Dashboard';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/auth/callback" element={<OAuthCallback />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/" element={<Login />} />
      </Routes>
    </Router>
  );
}

export default App;
```

## 📦 Dependencies yang Diperlukan

```bash
npm install @supabase/supabase-js react-router-dom
```

## 🔄 Flow OAuth yang Benar

1. **User klik "Continue with Google"** → `supabase.auth.signInWithOAuth()`
2. **Supabase** → Redirect ke Google OAuth
3. **Google** → User login & consent
4. **Google** → Redirect ke `http://localhost:3001/auth/callback#access_token=...`
5. **Frontend** → Extract `access_token` dari URL fragment
6. **Frontend** → POST ke `/api/v1/auth/supabase-callback` dengan `access_token`
7. **Backend** → Verify token, create user, return custom JWT
8. **Frontend** → Store tokens, redirect to dashboard

## ✅ Backend Sudah Diperbaiki

Backend sudah ditambahkan endpoint `/api/v1/auth/supabase-callback` untuk menerima `access_token` dari frontend.
