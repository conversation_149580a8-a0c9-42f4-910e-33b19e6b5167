"""
Authentication API endpoints
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Response, BackgroundTasks, Request
from fastapi.responses import RedirectResponse
from datetime import datetime, timedelta
from supabase import Client
from sqlalchemy.orm import Session
from app.core.database import get_supabase, get_db, get_db_optional
from app.core.auth import get_current_user, get_current_user_optional, AuthService, verify_supabase_jwt
from app.core.config import settings
from app.models.user import UserProfile
from app.schemas.auth import (
    UserSignupRequest, UserLoginRequest, UserResponse, AuthResponse,
    UserProfileUpdate, EmailVerificationRequest, ResendVerificationRequest,
    ForgotPasswordRequest, ResetPasswordRequest, ChangePasswordRequest,
    AuthStatusResponse, MessageResponse, UserPublicResponse
)
from app.services.email_service import email_service
import logging
import secrets
from supabase import create_client
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
import urllib.parse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["Authentication"])


# Helper functions
async def get_user_by_email(db: Session, email: str) -> Optional[UserProfile]:
    """Get user by email from database"""
    try:
        # First try to get from Supabase auth.users
        supabase = get_supabase()
        auth_response = supabase.auth.admin.list_users()

        auth_user = None
        for user in auth_response.users:
            if user.email == email:
                auth_user = user
                break

        if not auth_user:
            return None

        # Get user profile
        profile = db.query(UserProfile).filter(UserProfile.id == auth_user.id).first()
        return profile

    except Exception as e:
        logger.error(f"Error getting user by email: {e}")
        return None


async def create_user_with_profile(db: Session, supabase: Client, email: str, password: str, full_name: Optional[str] = None) -> UserProfile:
    """Create user in Supabase auth and profile in database"""
    try:
        # Create user in Supabase auth
        auth_response = supabase.auth.admin.create_user({
            "email": email,
            "password": password,
            "email_confirm": False  # We'll handle email verification manually
        })

        if not auth_response.user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create user account"
            )

        # Generate verification token
        verification_token = email_service.generate_verification_token()
        verification_expires = email_service.get_token_expiry(24)  # 24 hours

        # Create user profile
        user_profile = UserProfile(
            id=auth_response.user.id,
            username=email.split('@')[0],  # Default username from email
            full_name=full_name,
            is_verified=False,
            verification_token=verification_token,
            verification_token_expires=verification_expires,
            provider='email'
        )

        db.add(user_profile)
        db.commit()
        db.refresh(user_profile)

        return user_profile

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )


# API Endpoints

@router.post("/signup", response_model=MessageResponse)
async def signup(
    user_data: UserSignupRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    supabase: Client = Depends(get_supabase)
):
    """User signup endpoint"""
    try:
        # Check if user already exists
        existing_user = await get_user_by_email(db, user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create user with profile
        user_profile = await create_user_with_profile(
            db, supabase, user_data.email, user_data.password, user_data.full_name
        )

        # Send verification email in background
        background_tasks.add_task(
            email_service.send_verification_email,
            user_data.email,
            user_profile.verification_token,
            user_profile.full_name
        )

        return MessageResponse(
            message="Account created successfully. Please check your email to verify your account.",
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Signup error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create account"
        )


@router.post("/login", response_model=AuthResponse)
async def login(
    user_data: UserLoginRequest,
    db: Session = Depends(get_db),
    supabase: Client = Depends(get_supabase)
):
    """User login endpoint"""
    try:
        # Authenticate with Supabase
        auth_response = supabase.auth.sign_in_with_password({
            "email": user_data.email,
            "password": user_data.password
        })

        if not auth_response.user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Get user profile
        user_profile = db.query(UserProfile).filter(
            UserProfile.id == auth_response.user.id
        ).first()

        if not user_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )

        # Check if email is verified (for email provider)
        if user_profile.provider == 'email' and not user_profile.is_verified:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Please verify your email address before logging in"
            )

        # Update login tracking
        user_profile.last_login = datetime.utcnow()
        user_profile.login_count += 1
        db.commit()

        # Create custom JWT token
        auth_service = AuthService(supabase)
        access_token = auth_service.create_access_token(
            data={"sub": str(auth_response.user.id), "email": auth_response.user.email}
        )

        return AuthResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserResponse(
                id=str(user_profile.id),
                email=auth_response.user.email,
                username=user_profile.username,
                full_name=user_profile.full_name,
                avatar_url=user_profile.avatar_url,
                is_verified=user_profile.is_verified,
                provider=user_profile.provider,
                last_login=user_profile.last_login.isoformat() if user_profile.last_login else None,
                login_count=user_profile.login_count,
                created_at=user_profile.created_at.isoformat()
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.get("/verify")
async def verify_email(
    token: str,
    db: Session = Depends(get_db)
):
    """Email verification endpoint"""
    try:
        # Find user by verification token
        user_profile = db.query(UserProfile).filter(
            UserProfile.verification_token == token
        ).first()

        if not user_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification token"
            )

        # Check if token is expired
        if user_profile.verification_token_expires and user_profile.verification_token_expires < datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification token has expired"
            )

        # Verify user
        user_profile.is_verified = True
        user_profile.verification_token = None
        user_profile.verification_token_expires = None
        db.commit()

        # Redirect to frontend success page
        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/auth/verified",
            status_code=status.HTTP_302_FOUND
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification error: {e}")
        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/auth/verification-error",
            status_code=status.HTTP_302_FOUND
        )


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    profile_update: UserProfileUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Update user profile"""
    
    try:
        # Prepare update data
        update_data = {}
        if profile_update.username is not None:
            update_data["username"] = profile_update.username
        if profile_update.full_name is not None:
            update_data["full_name"] = profile_update.full_name
        if profile_update.avatar_url is not None:
            update_data["avatar_url"] = profile_update.avatar_url
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields to update"
            )
        
        # Update profile in database
        response = supabase.table("user_profiles").update(update_data).eq("id", current_user["user_id"]).execute()
        
        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )
        
        updated_profile = response.data[0]
        
        return UserResponse(
            id=current_user["user_id"],
            email=current_user["email"],
            username=updated_profile.get("username"),
            full_name=updated_profile.get("full_name"),
            avatar_url=updated_profile.get("avatar_url"),
            created_at=updated_profile.get("created_at", "")
        )
        
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )


class OAuthCallbackData(BaseModel):
    access_token: str
    refresh_token: str = None

@router.post("/callback")
async def auth_callback_post(
    data: OAuthCallbackData,
    db: Session = Depends(get_db),
    supabase: Client = Depends(get_supabase)
):
    """
    Handle callback from Supabase Auth (after OAuth login) via POST body.
    Expects access_token, refresh_token in JSON body.
    """
    # Tambahkan logging
    logger.info(f"POST /callback received: access_token={data.access_token}, refresh_token={data.refresh_token}")

    access_token = data.access_token
    refresh_token = data.refresh_token
    if not access_token:
        logger.warning("No access_token received in POST /callback")
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=no_token")

    # 1. Verifikasi JWT Supabase
    try:
        payload = verify_supabase_jwt(access_token)
        user_id = payload.get("sub")
        email = payload.get("email")
    except Exception as e:
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=invalid_token")

    # 2. Cek/otomatis buat user di database backend
    user_profile = db.query(UserProfile).filter(UserProfile.id == user_id).first()
    if not user_profile:
        user_profile = UserProfile(
            id=user_id,
            username=email.split("@")[0],
            full_name=payload.get("user_metadata", {}).get("full_name"),
            avatar_url=payload.get("user_metadata", {}).get("avatar_url"),
            is_verified=True,
            provider="google"
        )
        db.add(user_profile)
        db.commit()
        db.refresh(user_profile)

    # 3. Redirect ke dashboard (atau set session jika perlu)
    return RedirectResponse(url=f"{settings.FRONTEND_URL}/dashboard")


@router.post("/logout")
async def logout(
    current_user: Dict[str, Any] = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Logout current user"""
    
    try:
        # Supabase handles logout client-side, but we can log the event
        logger.info(f"User {current_user['user_id']} logged out")
        
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/status")
async def auth_status(
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user_optional)
):
    """Check authentication status"""
    
    if current_user:
        profile = current_user.get("profile", {})
        return {
            "authenticated": True,
            "user": {
                "id": current_user["user_id"],
                "email": current_user["email"],
                "username": profile.get("username"),
                "full_name": profile.get("full_name"),
                "avatar_url": profile.get("avatar_url")
            }
        }
    
    return {"authenticated": False, "user": None}


# OAuth Endpoints
@router.get("/oauth/google")
async def google_oauth_login():
    """Initiate Google OAuth login flow"""
    try:
        # Google OAuth 2.0 parameters
        google_client_id = settings.GOOGLE_CLIENT_ID
        redirect_uri = settings.GOOGLE_REDIRECT_URI
        scope = "email profile"

        # Build OAuth URL
        oauth_url = (
            f"https://accounts.google.com/o/oauth2/v2/auth?"
            f"client_id={google_client_id}&"
            f"redirect_uri={urllib.parse.quote(redirect_uri)}&"
            f"response_type=code&"
            f"scope={urllib.parse.quote(scope)}&"
            f"access_type=offline&"
            f"prompt=consent"
        )

        logger.info(f"Redirecting to Google OAuth: {oauth_url}")
        return RedirectResponse(url=oauth_url)

    except Exception as e:
        logger.error(f"Google OAuth initiation error: {e}")
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=oauth_init_failed")


@router.post("/supabase-callback")
async def supabase_auth_callback(
    data: OAuthCallbackData,
    db: Session = Depends(get_db)
):
    """
    Handle Supabase Auth callback - for frontend to send access_token
    """
    try:
        access_token = data.access_token
        if not access_token:
            logger.warning("No access_token received in Supabase callback")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Access token is required"
            )

        # Verify Supabase JWT token
        payload = verify_supabase_jwt(access_token)
        user_id = payload.get("sub")
        email = payload.get("email")

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid token payload"
            )

        # Create or update user profile in database
        user_profile = db.query(UserProfile).filter(UserProfile.id == user_id).first()
        if not user_profile:
            user_metadata = payload.get("user_metadata", {})
            user_profile = UserProfile(
                id=user_id,
                username=email.split("@")[0],
                full_name=user_metadata.get("full_name"),
                avatar_url=user_metadata.get("avatar_url"),
                is_verified=True,
                provider="google"
            )
            db.add(user_profile)
            db.commit()
            db.refresh(user_profile)
            logger.info(f"Created new user profile for {email}")
        else:
            # Update last login
            user_profile.last_login = datetime.utcnow()
            user_profile.login_count += 1
            db.commit()
            logger.info(f"Updated existing user profile for {email}")

        # Create custom JWT token for frontend
        auth_service = AuthService(get_supabase())
        custom_access_token = auth_service.create_access_token(
            data={"sub": str(user_id), "email": email}
        )

        return {
            "success": True,
            "access_token": custom_access_token,
            "user": {
                "id": user_id,
                "email": email,
                "username": user_profile.username,
                "full_name": user_profile.full_name,
                "avatar_url": user_profile.avatar_url
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Supabase callback error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/setup-profile")
async def setup_user_profile(
    current_user: Dict[str, Any] = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Setup user profile after first OAuth login"""
    
    try:
        auth_service = AuthService(supabase)
        
        # Check if profile already exists
        existing_profile = await auth_service.get_user_profile(current_user["user_id"])
        
        if existing_profile:
            return {
                "message": "Profile already exists",
                "profile": existing_profile
            }
        
        # Create new profile
        # Note: This would typically get user data from the JWT or Supabase auth
        user_data = {
            "id": current_user["user_id"],
            "email": current_user["email"]
        }
        
        profile = await auth_service.create_user_profile(user_data)
        
        return {
            "message": "Profile created successfully",
            "profile": profile
        }
        
    except Exception as e:
        logger.error(f"Error setting up user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to setup profile"
        )


@router.get("/callback")
async def auth_callback_get(
    code: str = None,
    error: str = None,
    access_token: str = None,
    refresh_token: str = None,
    db: Session = Depends(get_db)
):
    """
    Handle OAuth callback - supports both direct OAuth and Supabase Auth
    """
    try:
        if error:
            logger.error(f"OAuth error: {error}")
            return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error={error}")

        # Handle Supabase Auth callback (access_token in query params)
        if access_token:
            logger.info("Supabase Auth callback detected with access_token")
            try:
                # Verify Supabase JWT token
                payload = verify_supabase_jwt(access_token)
                user_id = payload.get("sub")
                email = payload.get("email")

                if not user_id or not email:
                    logger.error("Invalid token payload")
                    return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=invalid_token")

                # Create or update user profile in database
                user_profile = db.query(UserProfile).filter(UserProfile.id == user_id).first()
                if not user_profile:
                    user_metadata = payload.get("user_metadata", {})
                    user_profile = UserProfile(
                        id=user_id,
                        username=email.split("@")[0],
                        full_name=user_metadata.get("full_name"),
                        avatar_url=user_metadata.get("avatar_url"),
                        is_verified=True,
                        provider="google"
                    )
                    db.add(user_profile)
                    db.commit()
                    db.refresh(user_profile)
                    logger.info(f"Created new user profile for {email}")
                else:
                    # Update last login
                    user_profile.last_login = datetime.utcnow()
                    user_profile.login_count += 1
                    db.commit()
                    logger.info(f"Updated existing user profile for {email}")

                # Create custom JWT token for frontend
                auth_service = AuthService(get_supabase())
                custom_access_token = auth_service.create_access_token(
                    data={"sub": str(user_id), "email": email}
                )

                # Redirect to frontend with success
                redirect_url = (
                    f"{settings.FRONTEND_URL}/auth/callback?"
                    f"access_token={custom_access_token}&"
                    f"user_id={user_id}&"
                    f"email={urllib.parse.quote(email)}"
                )

                logger.info(f"Supabase OAuth success, redirecting to: {redirect_url}")
                return RedirectResponse(url=redirect_url)

            except Exception as e:
                logger.error(f"Supabase token verification error: {e}")
                return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=token_verification_failed")

        # Handle direct OAuth callback (authorization code)
        if not code:
            logger.error("No authorization code or access token received")
            return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=no_code")

        # Exchange code for tokens with Google
        google_client_id = settings.GOOGLE_CLIENT_ID
        google_client_secret = settings.GOOGLE_CLIENT_SECRET
        redirect_uri = settings.GOOGLE_REDIRECT_URI

        if not google_client_secret:
            logger.error("Google Client Secret not configured")
            return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=config_missing")

        # Get tokens from Google
        import httpx
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://oauth2.googleapis.com/token",
                data={
                    "client_id": google_client_id,
                    "client_secret": google_client_secret,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": redirect_uri,
                }
            )

            if token_response.status_code != 200:
                logger.error(f"Token exchange failed: {token_response.text}")
                return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=token_exchange_failed")

            tokens = token_response.json()
            access_token = tokens.get("access_token")

            if not access_token:
                logger.error("No access token received from Google")
                return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=no_access_token")

            # Get user info from Google
            user_response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {access_token}"}
            )

            if user_response.status_code != 200:
                logger.error(f"Failed to get user info: {user_response.text}")
                return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=user_info_failed")

            user_info = user_response.json()

            # Create or get user in Supabase
            supabase = get_supabase()

            # Try to sign in with Google email
            try:
                # Check if user exists in Supabase
                auth_response = supabase.auth.admin.list_users()
                existing_user = None

                for user in auth_response.users:
                    if user.email == user_info["email"]:
                        existing_user = user
                        break

                if not existing_user:
                    # Create user in Supabase
                    create_response = supabase.auth.admin.create_user({
                        "email": user_info["email"],
                        "email_confirm": True,
                        "user_metadata": {
                            "full_name": user_info.get("name"),
                            "avatar_url": user_info.get("picture"),
                            "provider": "google"
                        }
                    })
                    user_id = create_response.user.id
                else:
                    user_id = existing_user.id

                # Create or update user profile in database
                user_profile = db.query(UserProfile).filter(UserProfile.id == user_id).first()
                if not user_profile:
                    user_profile = UserProfile(
                        id=user_id,
                        username=user_info["email"].split("@")[0],
                        full_name=user_info.get("name"),
                        avatar_url=user_info.get("picture"),
                        is_verified=True,
                        provider="google"
                    )
                    db.add(user_profile)
                    db.commit()
                    db.refresh(user_profile)

                # Create custom JWT token for frontend
                auth_service = AuthService(supabase)
                custom_access_token = auth_service.create_access_token(
                    data={"sub": str(user_id), "email": user_info["email"]}
                )

                # Redirect to frontend with tokens
                redirect_url = (
                    f"{settings.FRONTEND_URL}/auth/callback?"
                    f"access_token={custom_access_token}&"
                    f"user_id={user_id}&"
                    f"email={urllib.parse.quote(user_info['email'])}"
                )

                logger.info(f"OAuth success, redirecting to: {redirect_url}")
                return RedirectResponse(url=redirect_url)

            except Exception as e:
                logger.error(f"Supabase user creation error: {e}")
                return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=user_creation_failed")

    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/error?error=callback_failed")
